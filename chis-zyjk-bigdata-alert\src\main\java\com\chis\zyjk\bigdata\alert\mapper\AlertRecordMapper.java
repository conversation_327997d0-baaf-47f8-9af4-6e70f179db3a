package com.chis.zyjk.bigdata.alert.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.chis.zyjk.bigdata.alert.pojo.po.AlertRecordPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 预警记录表Mapper
 */
public interface AlertRecordMapper extends BaseMapper<AlertRecordPO> {

    /**
     * 根据自定义SQL条件查询记录列表
     *
     * @param whereSql     WHERE子句SQL（不包含WHERE关键字）
     * @param dedupValues  去重值列表，用于IN条件过滤（可选）
     * @param orderByField 排序字段
     * @return 预警记录列表
     */
    List<AlertRecordPO> listByCustomSql(@Param("whereSql") String whereSql,
                                        @Param("dedupValues") List<String> dedupValues,
                                        @Param("orderByField") String orderByField);

    /**
     * 根据自定义SQL条件查询记录列表且根据分组及排序仅查询每组前n条的数据
     *
     * @param whereSql     WHERE子句SQL（不包含WHERE关键字）
     * @param dedupValues  去重值列表，用于IN条件过滤（可选）
     * @param orderByField 排序字段
     * @param groupByField 分组字段
     * @param topN         每组取前N条
     * @return 预警记录列表
     */
    List<AlertRecordPO> listTopNByCustomSql(@Param("whereSql") String whereSql,
                                            @Param("dedupValues") List<String> dedupValues,
                                            @Param("orderByField") String orderByField,
                                            @Param("groupByField") String groupByField,
                                            @Param("topN") int topN);

}
