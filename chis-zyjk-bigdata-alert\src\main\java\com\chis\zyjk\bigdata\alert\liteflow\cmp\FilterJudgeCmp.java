package com.chis.zyjk.bigdata.alert.liteflow.cmp;

import com.chis.project.frame.common.tools.core.util.ObjectUtil;
import com.chis.project.frame.common.tools.json.JSONObject;
import com.chis.zyjk.bigdata.alert.liteflow.common.ContextHelper;
import com.chis.zyjk.bigdata.alert.liteflow.core.ChisNodeComponent;
import com.chis.zyjk.bigdata.alert.liteflow.pojo.context.CmpContext;
import com.chis.zyjk.bigdata.alert.liteflow.pojo.dto.FilterResult;
import com.chis.zyjk.bigdata.alert.liteflow.utils.SpELExpressionUtils;
import com.chis.zyjk.bigdata.alert.pojo.context.AlertCmpContext;
import com.chis.zyjk.bigdata.alert.pojo.context.AlertGlobalContext;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.slot.DefaultContext;

import java.util.List;

/**
 * 数据过滤判断组件
 * <p>
 * <b>filterJudge</b>
 */
@LiteflowComponent(id = "filterJudge", name = "数据过滤判断组件")
public class FilterJudgeCmp extends ChisNodeComponent<AlertGlobalContext, AlertCmpContext> {

    @Override
    public void doProcess() {
        List<Object> inputDataList = getInputDataByPath(config.getStr("inputDataPath"));

        if (ObjectUtil.isEmpty(inputDataList)) {
            outputResult(new FilterResult(), cmp);
            return;
        }

        // 执行过滤处理（使用工具类）
        FilterResult result = filterData(config, inputDataList, cmp);

        // 固定字段输出
        outputResult(result, cmp);
    }

    @Override
    public String[] getMustConfigList() {
        return new String[]{"inputDataPath", "condition"};
    }

    /**
     * 输出过滤结果
     *
     * @param result  过滤结果
     * @param context LiteFlow上下文
     */
    public void outputResult(FilterResult result, DefaultContext context) {
        ContextHelper.setContextData(this.getTag(), "matchedData", result.getMatchedData(), context);
        ContextHelper.setContextData(this.getTag(), "unmatchedData", result.getUnmatchedData(), context);
    }

    /**
     * 过滤数据
     *
     * @param config    节点配置
     * @param inputList 输入数据列表
     * @param context   数据上下文
     * @return 过滤结果
     */
    private FilterResult filterData(JSONObject config, List<Object> inputList, CmpContext context) {
        FilterResult result = new FilterResult();

        String condition = config.getStr("condition");
        for (Object record : inputList) {
            ContextHelper.setContextData(this.getTag(), "record", record, context);
            // 使用SpEL 表达式工具类评估条件
            boolean matches = SpELExpressionUtils.evaluateCondition(condition, contextBeanList);

            if (matches) {
                result.getMatchedData().add(record);
            } else {
                result.getUnmatchedData().add(record);
            }
        }
        return result;
    }
}
