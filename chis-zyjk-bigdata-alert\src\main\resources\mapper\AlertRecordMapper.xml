<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chis.zyjk.bigdata.alert.mapper.AlertRecordMapper">

    <resultMap id="BaseResultMap" type="com.chis.zyjk.bigdata.alert.pojo.po.AlertRecordPO">
        <id property="id" column="id" jdbcType="VARCHAR"/>
        <result property="ruleCode" column="rule_code" jdbcType="VARCHAR"/>
        <result property="dedupValue" column="dedup_value" jdbcType="VARCHAR"/>
        <result property="changeType" column="change_type" jdbcType="VARCHAR"/>
        <result property="alertLevel" column="alert_level" jdbcType="VARCHAR"/>
        <result property="alertValue" column="alert_value" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="TINYINT"/>
        <result property="alertContent" column="alert_content" jdbcType="LONGVARCHAR"/>
        <result property="alertJson" column="alert_json" jdbcType="LONGVARCHAR"/>
        <result property="sourceData" column="source_data" jdbcType="LONGVARCHAR"/>
        <result property="revision" column="revision" jdbcType="INTEGER"/>
        <result property="delFlag" column="del_flag" jdbcType="CHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, rule_code, dedup_value, change_type, alert_level, alert_value, status,
        alert_content, alert_json, source_data, revision, del_flag, create_time,
        create_by, update_time, update_by
    </sql>

    <!-- 根据自定义SQL条件查询记录列表 -->
    <select id="listByCustomSql" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM alert_record
        WHERE del_flag = '0'
        <if test="dedupValues != null and dedupValues.size() > 0">
            AND dedup_value IN
            <foreach collection="dedupValues" item="dedupValue" open="(" separator="," close=")">
                #{dedupValue}
            </foreach>
        </if>
        <if test="whereSql != null and whereSql != ''">
            AND (${whereSql})
        </if>
        ORDER BY ${orderByField} DESC
    </select>

    <!-- 根据自定义SQL条件查询记录列表且根据分组及排序仅查询每组前n条的数据 -->
    <select id="listTopNByCustomSql" resultMap="BaseResultMap">
        SELECT * FROM (
            SELECT <include refid="Base_Column_List"/>,
                   ROW_NUMBER() OVER (
                       PARTITION BY ${groupByField}
                       ORDER BY ${orderByField}
                   ) as rn
            FROM alert_record
            WHERE del_flag = '0'
            <if test="dedupValues != null and dedupValues.size() > 0">
                AND dedup_value IN
                <foreach collection="dedupValues" item="dedupValue" open="(" separator="," close=")">
                    #{dedupValue}
                </foreach>
            </if>
            <if test="whereSql != null and whereSql != ''">
                AND (${whereSql})
            </if>
        ) ranked
        WHERE rn &lt;= #{topN}
        ORDER BY ${orderByField}, rn
    </select>

</mapper>
