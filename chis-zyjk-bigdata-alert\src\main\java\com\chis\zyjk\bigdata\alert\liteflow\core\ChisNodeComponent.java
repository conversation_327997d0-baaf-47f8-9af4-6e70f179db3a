package com.chis.zyjk.bigdata.alert.liteflow.core;

import cn.hutool.core.lang.Tuple;
import com.chis.project.frame.common.tools.core.collection.CollUtil;
import com.chis.project.frame.common.tools.core.convert.Convert;
import com.chis.project.frame.common.tools.core.convert.ConvertException;
import com.chis.project.frame.common.tools.core.util.ObjectUtil;
import com.chis.project.frame.common.tools.core.util.StrUtil;
import com.chis.project.frame.common.tools.json.JSONArray;
import com.chis.project.frame.common.tools.json.JSONObject;
import com.chis.zyjk.bigdata.alert.liteflow.exception.LiteFlowCmpException;
import com.chis.zyjk.bigdata.alert.liteflow.exception.LiteFlowErrorCode;
import com.chis.zyjk.bigdata.alert.liteflow.exception.LiteFlowExceptionHelper;
import com.chis.zyjk.bigdata.alert.liteflow.exception.LiteFlowRuleException;
import com.chis.zyjk.bigdata.alert.liteflow.pojo.context.CmpContext;
import com.chis.zyjk.bigdata.alert.liteflow.pojo.context.GlobalContext;
import com.chis.zyjk.bigdata.alert.liteflow.utils.VariableReplacerUtils;
import com.yomahub.liteflow.core.FlowExecutor;
import com.yomahub.liteflow.core.NodeComponent;
import com.yomahub.liteflow.exception.NoSuchContextBeanException;
import com.yomahub.liteflow.log.LFLog;
import com.yomahub.liteflow.log.LFLoggerManager;
import com.yomahub.liteflow.util.LiteflowContextRegexMatcher;

import java.util.ArrayList;
import java.util.List;

public abstract class ChisNodeComponent<G extends GlobalContext, C extends CmpContext> extends NodeComponent {
    protected final LFLog logger = LFLoggerManager.getLogger(FlowExecutor.class);

    protected String tag;
    protected G global;
    protected C cmp;
    protected JSONObject config;
    protected JSONObject data;
    protected String nodeId;
    protected String name;
    protected List<Tuple> contextBeanList;

    public void beforeDoProcess() {
        this.global = getContextBean("global");
        if (ObjectUtil.isEmpty(this.global)) {
            throw LiteFlowExceptionHelper.createRuleException(LiteFlowErrorCode.RULE_EMPTY);
        }
        this.tag = this.getTag();
        if (ObjectUtil.isEmpty(this.tag)) {
            throw LiteFlowExceptionHelper.createNodeException(LiteFlowErrorCode.CMP_TAG_MISSING, this);
        }
        this.cmp = getContextBean("cmp");
        if (ObjectUtil.isEmpty(this.cmp)) {
            throw LiteFlowExceptionHelper.createNodeException(LiteFlowErrorCode.CMP_CONFIG_CONTEXT_EMPTY, this);
        }
        this.config = Convert.convert(JSONObject.class, searchContextConfig(""));
        if (ObjectUtil.isEmpty(this.config)) {
            throw LiteFlowExceptionHelper.createNodeException(LiteFlowErrorCode.CMP_CONFIG_CONTEXT_EMPTY, this);
        }
        checkMustConfig(getMustConfigList());
        this.nodeId = this.getNodeId();
        this.name = this.getName();
        this.data = Convert.convert(JSONObject.class, searchContextData(""));
        this.contextBeanList = this.getSlot().getContextBeanList();
    }

    public String[] getMustConfigList() {
        return new String[]{};
    }

    /**
     * 验证组件必需配置
     *
     * @param configStrList 必填配置名称列表
     */
    public void checkMustConfig(String[] configStrList) {
        if (ObjectUtil.isEmpty(configStrList)) {
            return;
        }
        List<String> paramList = new ArrayList<>();
        for (String configStr : configStrList) {
            if (ObjectUtil.isEmpty(this.config)) {
                paramList.add(configStr);
            } else {
                if (!this.config.containsKey(configStr) && ObjectUtil.isEmpty(this.config.get(configStr))) {
                    paramList.add(configStr);
                }
            }
        }

        if (paramList.isEmpty()) {
            return;
        }

        String errorMsg = CollUtil.join(paramList, "、");

        throw LiteFlowExceptionHelper.createNodeException(
                LiteFlowErrorCode.CMP_CONFIG_VALIDATION_FAILED,
                LiteFlowErrorCode.CMP_CONFIG_VALIDATION_FAILED.getDesc() + "：缺少[" + errorMsg + "]配置，请检查配置信息",
                this
        );
    }

    @Override
    public void process() {
        try {
            beforeDoProcess();
            doProcess();
        } catch (LiteFlowCmpException e) {
            throw e;
        } catch (NoSuchContextBeanException e) {
            throw LiteFlowExceptionHelper.createNodeException(
                    e,
                    LiteFlowErrorCode.CMP_CONFIG_CONTEXT_EMPTY,
                    this
            );
        } catch (Exception e) {
            throw LiteFlowExceptionHelper.createNodeException(
                    e,
                    LiteFlowErrorCode.CMP_EXECUTION_FAILED,
                    name + "执行失败: " + e.getMessage(),
                    this
            );
        }
    }

    public abstract void doProcess() throws Exception;

    @Override
    public void afterProcess() {
        super.afterProcess();
        CmpContext cmpContext = this.getContextBean(CmpContext.class);
        if (ObjectUtil.isNotEmpty(cmpContext)) {
            String contextKey = "[" + this.getTag() + ":" + this.getNodeId() + ":" + this.getName() + "]";
            logger.debug("{}上下文: {}", contextKey, cmpContext.getDataMap().toString());
        }
    }

    /**
     * 获取输入数据
     *
     * @param inputDataPaths 输入数据路径
     * @return 输入数据
     */
    public List<Object> getInputDataByPaths(String inputDataPaths) {
        // 获取输入数据字段名
        JSONArray inputDataPathsArray = config.getJSONArray(inputDataPaths);
        if (inputDataPathsArray == null) {
            logger.warn("输入数据字段名为空，nodeId: {}, tag: {},", nodeId, tag);
            return new ArrayList<>();
        }

        List<Object> inputDataList = new ArrayList<>();
        for (Object inputDataPath : inputDataPathsArray) {
            String inputFieldStr = Convert.toStr(inputDataPath, "");
            inputDataList.add(getInputDataByPath(inputFieldStr));
        }

        if (ObjectUtil.isEmpty(inputDataList)) {
            logger.warn("输入数据列表为空，nodeId: {}, tag: {}", nodeId, tag);
            return new ArrayList<>();
        }
        return inputDataList;
    }

    public List<Object> getInputDataByPath(String inputDataPath) {
        String inputFieldStr = Convert.toStr(inputDataPath, "");
        if (ObjectUtil.isEmpty(inputFieldStr)) {
            logger.warn("输入数据字段名为空，nodeId: {}, tag: {}, inputDataPath: {}", nodeId, tag, inputDataPath);
            return new ArrayList<>();
        }

        Object inputData = VariableReplacerUtils.resolveExpression(this.tag, inputFieldStr, contextBeanList);

        if (inputData == null) {
            logger.warn("输入数据为空，nodeId: {}, tag: {}, inputDataPath: {}", nodeId, tag, inputDataPath);
            return new ArrayList<>();
        }

        // 转换为列表
        return convertToList(inputData);
    }

    public Object searchContext(String expression) {
        return LiteflowContextRegexMatcher.searchContext(this.getSlot().getContextBeanList(), expression);
    }

    public Object searchContextGlobal(String expression) {
        return searchContext("global" + addPointExpression(expression));
    }

    public Object searchContextConfig(String expression) {
        return searchContextConfigByTag(this.getTag(), expression);
    }

    public Object searchContextConfigByTag(String tag, String expression) {
        return searchContext("cmp.getData('" + tag + "').config" + addPointExpression(expression));
    }

    public Object searchContextData(String expression) {
        return searchContextDataByTag(this.getTag(), expression);
    }

    public Object searchContextDataByTag(String tag, String expression) {
        return searchContext("cmp.getData('" + tag + "').config" + addPointExpression(expression));
    }

    public String addPointExpression(String expression) {
        if (StrUtil.isBlank(expression)) {
            return "";
        }
        return "." + expression;
    }

    /**
     * 转换为列表
     *
     * @param data 数据
     * @return 列表
     */
    @SuppressWarnings("unchecked")
    private List<Object> convertToList(Object data) {
        if (data instanceof List) {
            return (List<Object>) data;
        } else {
            List<Object> list = new ArrayList<>();
            list.add(data);
            return list;
        }
    }
}
